"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

interface User {
  id: string
  email: string
  name: string
  role: "user" | "admin"
  tier: 0 | 1 | 2
}

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireRole?: "user" | "admin"
  requireTier?: 0 | 1 | 2
  fallback?: React.ReactNode
}

export function AuthGuard({ children, requireAuth = true, requireRole, requireTier, fallback }: AuthGuardProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch("/api/auth/me", {
        credentials: "include",
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        setUser(null)
        if (requireAuth) {
          router.push("/login")
          return
        }
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
      if (requireAuth) {
        router.push("/login")
        return
      }
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (requireAuth && !user) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <p>Redirecting to login...</p>
        </div>
      )
    )
  }

  if (requireRole && user?.role !== requireRole) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <p>Access denied. Insufficient permissions.</p>
        </div>
      )
    )
  }

  if (requireTier !== undefined && user && user.tier < requireTier) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Upgrade Required</h2>
            <p className="text-gray-400 mb-6">This feature requires a higher subscription tier.</p>
            <button
              onClick={() => router.push("/pricing")}
              className="bg-green-500 hover:bg-green-600 text-black px-6 py-2 rounded-lg font-medium"
            >
              View Plans
            </button>
          </div>
        </div>
      )
    )
  }

  return <>{children}</>
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch("/api/auth/me", {
        credentials: "include",
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ email, password }),
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
        return { success: true }
      } else {
        const error = await response.json()
        return { success: false, error: error.error }
      }
    } catch (error) {
      return { success: false, error: "Login failed" }
    }
  }

  const logout = async () => {
    try {
      await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include",
      })
      setUser(null)
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  return { user, loading, login, logout, checkAuth }
}
