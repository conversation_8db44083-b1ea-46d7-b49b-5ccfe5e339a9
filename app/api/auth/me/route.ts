import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get("session")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    // Decode session token
    const sessionData = JSON.parse(Buffer.from(sessionToken, "base64").toString())

    // Check if token is expired
    if (Date.now() > sessionData.exp) {
      cookieStore.delete("session")
      return NextResponse.json({ error: "Session expired" }, { status: 401 })
    }

    return NextResponse.json({
      user: {
        id: sessionData.userId,
        email: sessionData.email,
        role: sessionData.role,
        tier: sessionData.tier,
      },
    })
  } catch (error) {
    console.error("Auth check error:", error)
    return NextResponse.json({ error: "Invalid session" }, { status: 401 })
  }
}
