"use client"

import { useState, useEffect } from "react"
import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"
import { MusicPlayer } from "@/components/music-player"
import { TrackList } from "@/components/track-list"
import { AuthGuard, useAuth } from "@/lib/auth-guard"

const mockTracks = [
  {
    id: "1",
    title: "Summer Vibes",
    artist: "DJ Cool",
    album: "Beach Party",
    duration: 180,
    tempo: 128,
    tone: "C",
    genre: "Electronic",
    mood: "Happy",
    coverUrl: "/placeholder.svg?height=40&width=40",
    audioUrl: "/audio/track1.mp3",
    tier: 0 as const,
    isLiked: true,
    waveformData: Array.from({ length: 3000 }, () => Math.random() * 100),
  },
  {
    id: "2",
    title: "Midnight Dreams",
    artist: "Luna Star",
    album: "Nocturnal",
    duration: 240,
    tempo: 95,
    tone: "Am",
    genre: "Ambient",
    mood: "Calm",
    coverUrl: "/placeholder.svg?height=40&width=40",
    audioUrl: "/audio/track2.mp3",
    tier: 1 as const,
    isLiked: false,
    waveformData: Array.from({ length: 3000 }, () => Math.random() * 100),
  },
  {
    id: "3",
    title: "Rock Anthem",
    artist: "Thunder Band",
    album: "Electric Storm",
    duration: 210,
    tempo: 140,
    tone: "E",
    genre: "Rock",
    mood: "Energetic",
    coverUrl: "/placeholder.svg?height=40&width=40",
    audioUrl: "/audio/track3.mp3",
    tier: 2 as const,
    isLiked: false,
    waveformData: Array.from({ length: 3000 }, () => Math.random() * 100),
  },
]

export default function HomePage() {
  const { user } = useAuth()
  const [currentTrackId, setCurrentTrackId] = useState<string | undefined>()
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [volume, setVolume] = useState(75)

  const currentTrack = mockTracks.find((track) => track.id === currentTrackId)

  const handlePlay = (trackId: string) => {
    setCurrentTrackId(trackId)
    setIsPlaying(true)
    setCurrentTime(0)
  }

  const handlePause = () => {
    setIsPlaying(false)
  }

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const handleNext = () => {
    if (!currentTrackId) return
    const currentIndex = mockTracks.findIndex((track) => track.id === currentTrackId)
    const nextIndex = (currentIndex + 1) % mockTracks.length
    setCurrentTrackId(mockTracks[nextIndex].id)
    setCurrentTime(0)
  }

  const handlePrevious = () => {
    if (!currentTrackId) return
    const currentIndex = mockTracks.findIndex((track) => track.id === currentTrackId)
    const prevIndex = currentIndex === 0 ? mockTracks.length - 1 : currentIndex - 1
    setCurrentTrackId(mockTracks[prevIndex].id)
    setCurrentTime(0)
  }

  const handleSeek = (time: number) => {
    setCurrentTime(time)
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
  }

  const handleDownload = (trackId: string) => {
    console.log("Downloading track:", trackId)
    // Implement download logic
  }

  const handleAddToPlaylist = (trackId: string) => {
    console.log("Adding to playlist:", trackId)
    // Implement add to playlist logic
  }

  const handleToggleLike = (trackId: string) => {
    console.log("Toggling like for track:", trackId)
    // Implement like toggle logic
  }

  // Simulate time progression
  useEffect(() => {
    if (!isPlaying || !currentTrack) return

    const interval = setInterval(() => {
      setCurrentTime((prev) => {
        if (prev >= currentTrack.duration) {
          handleNext()
          return 0
        }
        return prev + 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isPlaying, currentTrack])

  return (
      <div className="flex h-screen bg-black text-white">
        {/* Sidebar */}
        <Sidebar userRole={user?.role} userTier={user?.tier} />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <Header user={user} />

          {/* Content */}
          <main className="flex-1 overflow-auto p-6 pb-32">
            <div className="max-w-7xl mx-auto">
              <div className="mb-8">
                <h1 className="text-3xl font-bold mb-2">Your Music Library</h1>
                <p className="text-gray-400">Discover and enjoy your favorite tracks</p>
              </div>

              <TrackList
                tracks={mockTracks}
                currentTrackId={currentTrackId}
                isPlaying={isPlaying}
                userTier={user?.tier || 0}
                onPlay={handlePlay}
                onPause={handlePause}
                onDownload={handleDownload}
                onAddToPlaylist={handleAddToPlaylist}
                onToggleLike={handleToggleLike}
              />
            </div>
          </main>
        </div>

        {/* Music Player */}
        <MusicPlayer
          currentTrack={currentTrack}
          isPlaying={isPlaying}
          onPlayPause={handlePlayPause}
          onNext={handleNext}
          onPrevious={handlePrevious}
          onSeek={handleSeek}
          currentTime={currentTime}
          volume={volume}
          onVolumeChange={handleVolumeChange}
        />
      </div>
  )
}
