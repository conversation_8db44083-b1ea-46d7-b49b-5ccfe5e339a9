"use client"

import { useState, useEffect, useRef } from "react"
import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"
import { MusicPlayer } from "@/components/music-player"
import { TrackList } from "@/components/track-list"
import { AuthGuard, useAuth } from "@/lib/auth-guard"
import WaveSurfer from "wavesurfer.js"

const mockTracks = [
  {
    id: "1",
    title: "Summer Vibes",
    artist: "DJ Cool",
    album: "Beach Party",
    duration: 180,
    tempo: 128,
    tone: "C",
    genre: "Electronic",
    mood: "Happy",
    coverUrl: "/placeholder.svg?height=40&width=40",
    audioUrl: "/audio/track1.mp3",
    tier: 0 as const,
    isLiked: true,
    waveformData: Array.from({ length: 3000 }, () => Math.random() * 100),
  },
  {
    id: "2",
    title: "Midnight Dreams",
    artist: "Luna Star",
    album: "Nocturnal",
    duration: 240,
    tempo: 95,
    tone: "Am",
    genre: "Ambient",
    mood: "Calm",
    coverUrl: "/placeholder.svg?height=40&width=40",
    audioUrl: "/audio/track2.mp3",
    tier: 1 as const,
    isLiked: false,
    waveformData: Array.from({ length: 3000 }, () => Math.random() * 100),
  },
  {
    id: "3",
    title: "Rock Anthem",
    artist: "Thunder Band",
    album: "Electric Storm",
    duration: 210,
    tempo: 140,
    tone: "E",
    genre: "Rock",
    mood: "Energetic",
    coverUrl: "/placeholder.svg?height=40&width=40",
    audioUrl: "/audio/track3.mp3",
    tier: 2 as const,
    isLiked: false,
    waveformData: Array.from({ length: 3000 }, () => Math.random() * 100),
  },
]

export default function HomePage() {
  const { user } = useAuth()
  const [currentTrackId, setCurrentTrackId] = useState<string | undefined>()
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [volume, setVolume] = useState(75)
  const [duration, setDuration] = useState(0)
  const wavesurferRef = useRef<WaveSurfer | null>(null)

  const currentTrack = mockTracks.find((track) => track.id === currentTrackId)

  // Initialize WaveSurfer when track changes
  useEffect(() => {
    if (!currentTrack) return

    // Destroy existing instance
    if (wavesurferRef.current) {
      wavesurferRef.current.destroy()
    }

    // Create new WaveSurfer instance
    const wavesurfer = WaveSurfer.create({
      container: '#waveform',
      waveColor: '#374151',
      progressColor: '#22c55e',
      cursorColor: '#22c55e',
      barWidth: 2,
      barRadius: 3,
      height: 64,
      normalize: true,
      mediaControls: false,
    })

    wavesurferRef.current = wavesurfer

    // Load audio
    wavesurfer.load(currentTrack.audioUrl)

    // Event listeners
    wavesurfer.on('ready', () => {
      setDuration(wavesurfer.getDuration())
      wavesurfer.setVolume(volume / 100)
    })

    wavesurfer.on('audioprocess', () => {
      setCurrentTime(wavesurfer.getCurrentTime())
    })

    wavesurfer.on('seeking', () => {
      setCurrentTime(wavesurfer.getCurrentTime())
    })

    wavesurfer.on('finish', () => {
      handleNext()
    })

    wavesurfer.on('play', () => {
      setIsPlaying(true)
    })

    wavesurfer.on('pause', () => {
      setIsPlaying(false)
    })

    return () => {
      if (wavesurfer) {
        wavesurfer.destroy()
      }
    }
  }, [currentTrack])

  // Update volume when volume state changes
  useEffect(() => {
    if (wavesurferRef.current) {
      wavesurferRef.current.setVolume(volume / 100)
    }
  }, [volume])

  const handlePlay = (trackId: string) => {
    setCurrentTrackId(trackId)
    setCurrentTime(0)
  }

  const handlePause = () => {
    if (wavesurferRef.current) {
      wavesurferRef.current.pause()
    }
  }

  const handlePlayPause = () => {
    if (wavesurferRef.current) {
      wavesurferRef.current.playPause()
    }
  }

  const handleNext = () => {
    if (!currentTrackId) return
    const currentIndex = mockTracks.findIndex((track) => track.id === currentTrackId)
    const nextIndex = (currentIndex + 1) % mockTracks.length
    setCurrentTrackId(mockTracks[nextIndex].id)
    setCurrentTime(0)
  }

  const handlePrevious = () => {
    if (!currentTrackId) return
    const currentIndex = mockTracks.findIndex((track) => track.id === currentTrackId)
    const prevIndex = currentIndex === 0 ? mockTracks.length - 1 : currentIndex - 1
    setCurrentTrackId(mockTracks[prevIndex].id)
    setCurrentTime(0)
  }

  const handleSeek = (time: number) => {
    if (wavesurferRef.current && duration > 0) {
      const seekPosition = time / duration
      wavesurferRef.current.seekTo(seekPosition)
    }
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    if (wavesurferRef.current) {
      wavesurferRef.current.setVolume(newVolume / 100)
    }
  }

  const handleDownload = async (trackId: string) => {
    const track = mockTracks.find(t => t.id === trackId)
    if (!track) return

    try {
      const response = await fetch(track.audioUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `${track.artist} - ${track.title}.mp3`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  const handleAddToPlaylist = (trackId: string) => {
    console.log("Adding to playlist:", trackId)
    // Implement add to playlist logic
  }

  const handleToggleLike = (trackId: string) => {
    console.log("Toggling like for track:", trackId)
    // Implement like toggle logic
  }



  return (
      <div className="flex h-screen bg-black text-white">
        {/* Sidebar */}
        <Sidebar userRole={user?.role} userTier={user?.tier} />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          {/* <Header user={user} /> */}

          {/* Content */}
          <main className="flex-1 overflow-auto p-6 pb-36">
            <div className="max-w-7xl mx-auto">
              <div className="mb-8">
                <h1 className="text-3xl font-bold mb-2">Your Music Library</h1>
                <p className="text-gray-400">Discover and enjoy your favorite tracks</p>
              </div>

              <TrackList
                tracks={mockTracks}
                currentTrackId={currentTrackId}
                isPlaying={isPlaying}
                userTier={user?.tier || 0}
                onPlay={handlePlay}
                onPause={handlePause}
                onDownload={handleDownload}
                onAddToPlaylist={handleAddToPlaylist}
                onToggleLike={handleToggleLike}
              />
            </div>
          </main>
        </div>

        {/* Music Player */}
        <MusicPlayer
          currentTrack={currentTrack}
          isPlaying={isPlaying}
          onPlayPause={handlePlayPause}
          onNext={handleNext}
          onPrevious={handlePrevious}
          onSeek={handleSeek}
          currentTime={currentTime}
          volume={volume}
          onVolumeChange={handleVolumeChange}
          onDownload={handleDownload}
        />
      </div>
  )
}
