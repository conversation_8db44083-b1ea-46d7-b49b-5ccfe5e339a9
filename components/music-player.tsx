"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import {
  Play,
  Pause,
  SkipBack,
  Ski<PERSON>Forward,
  Volume2,
  VolumeX,
  Repeat,
  Shuffle,
  Heart,
  MoreHorizontal,
  Maximize2,
  Download,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"

interface Track {
  id: string
  title: string
  artist: string
  album?: string
  duration: number
  tempo: number
  tone: string
  genre: string
  mood: string
  coverUrl?: string
  audioUrl: string
  waveformData?: number[]
}

interface MusicPlayerProps {
  currentTrack?: Track
  isPlaying: boolean
  onPlayPause: () => void
  onNext: () => void
  onPrevious: () => void
  onSeek: (time: number) => void
  currentTime: number
  volume: number
  onVolumeChange: (volume: number) => void
  onDownload?: (trackId: string) => void
}

export function MusicPlayer({
  currentTrack,
  isPlaying,
  onPlayPause,
  onNext,
  onPrevious,
  onSeek,
  currentTime,
  volume,
  onVolumeChange,
  onDownload,
}: MusicPlayerProps) {
  const [isMuted, setIsMuted] = useState(false)
  const [isShuffled, setIsShuffled] = useState(false)
  const [repeatMode, setRepeatMode] = useState<"off" | "all" | "one">("off")
  const [isLiked, setIsLiked] = useState(false)

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }



  if (!currentTrack) {
    return (
      <div className="fixed bottom-0 left-0 right-0 h-24 border-t border-gray-800 bg-black">
        <div className="flex h-full items-center justify-center text-gray-400">
          <p>No track selected</p>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      className="fixed bottom-0 left-0 right-0 border-t border-gray-800 bg-black text-white"
    >
      {/* Waveform */}
      <div className="relative h-16 bg-gray-900">
        <div id="waveform" className="h-full w-full cursor-pointer" />
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="flex items-center gap-4 text-xs text-gray-400">
            <span>{formatTime(currentTime)}</span>
            <div className="flex gap-2">
              <Badge variant="outline" className="text-xs">
                {currentTrack.tempo} BPM
              </Badge>
              <Badge variant="outline" className="text-xs">
                {currentTrack.tone}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {currentTrack.genre}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {currentTrack.mood}
              </Badge>
            </div>
            <span>{formatTime(currentTrack.duration)}</span>
          </div>
        </div>
      </div>

      {/* Player Controls */}
      <div className="flex h-20 items-center justify-between px-4">
        {/* Track Info */}
        <div className="flex items-center gap-4 flex-1 min-w-0">
          <div className="h-14 w-14 rounded-lg bg-gray-800 overflow-hidden">
            {currentTrack.coverUrl ? (
              <img
                src={currentTrack.coverUrl || "/placeholder.svg"}
                alt={currentTrack.title}
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center">
                <span className="text-2xl">🎵</span>
              </div>
            )}
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="truncate font-medium">{currentTrack.title}</h4>
            <p className="truncate text-sm text-gray-400">{currentTrack.artist}</p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsLiked(!isLiked)}
            className={`${isLiked ? "text-green-500" : "text-gray-400"} hover:text-green-400`}
          >
            <Heart className={`h-5 w-5 ${isLiked ? "fill-current" : ""}`} />
          </Button>
        </div>

        {/* Main Controls */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsShuffled(!isShuffled)}
            className={`${isShuffled ? "text-green-500" : "text-gray-400"} hover:text-white`}
          >
            <Shuffle className="h-4 w-4" />
          </Button>

          <Button variant="ghost" size="icon" onClick={onPrevious} className="text-gray-400 hover:text-white">
            <SkipBack className="h-5 w-5" />
          </Button>

          <Button onClick={onPlayPause} className="h-10 w-10 rounded-full bg-white text-black hover:bg-gray-200">
            {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5 ml-0.5" />}
          </Button>

          <Button variant="ghost" size="icon" onClick={onNext} className="text-gray-400 hover:text-white">
            <SkipForward className="h-5 w-5" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              const modes: Array<"off" | "all" | "one"> = ["off", "all", "one"]
              const currentIndex = modes.indexOf(repeatMode)
              const nextMode = modes[(currentIndex + 1) % modes.length]
              setRepeatMode(nextMode)
            }}
            className={`${repeatMode !== "off" ? "text-green-500" : "text-gray-400"} hover:text-white`}
          >
            <Repeat className="h-4 w-4" />
            {repeatMode === "one" && <span className="absolute -top-1 -right-1 text-xs">1</span>}
          </Button>
        </div>

        {/* Volume & Additional Controls */}
        <div className="flex items-center gap-4 flex-1 justify-end">
          {onDownload && currentTrack && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDownload(currentTrack.id)}
              className="text-gray-400 hover:text-white"
            >
              <Download className="h-4 w-4" />
            </Button>
          )}

          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
            <MoreHorizontal className="h-5 w-5" />
          </Button>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setIsMuted(!isMuted)
                onVolumeChange(isMuted ? volume : 0)
              }}
              className="text-gray-400 hover:text-white"
            >
              {isMuted || volume === 0 ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
            <Slider
              value={[isMuted ? 0 : volume]}
              onValueChange={(value) => {
                onVolumeChange(value[0])
                setIsMuted(value[0] === 0)
              }}
              max={100}
              step={1}
              className="w-24"
            />
          </div>

          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
            <Maximize2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
